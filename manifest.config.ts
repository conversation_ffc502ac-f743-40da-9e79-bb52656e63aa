import { defineManifest } from '@crxjs/vite-plugin'
import pkg from './package.json'

export default defineManifest({
  manifest_version: 3,
  name: 'Netflix Skipper',
  description: 'Custom arrow key controls for Netflix video playback with configurable skip intervals',
  version: pkg.version,
  icons: {
    48: 'public/logo.png',
  },
  action: {
    default_icon: {
      48: 'public/logo.png',
    },
    default_popup: 'src/popup/index.html',
  },
  permissions: [
    'storage'
  ],
  content_scripts: [{
    js: ['src/content/main.tsx'],
    matches: ['https://www.netflix.com/*'],
    run_at: 'document_start'
  }],
})
