import { defineManifest } from '@crxjs/vite-plugin'
import pkg from './package.json'

export default defineManifest({
  manifest_version: 3,
  name: 'Netflix Skipper',
  description: 'Custom arrow key controls for Netflix video playback with configurable skip intervals',
  version: pkg.version,
  icons: {
    16: 'public/logo-16.png',
    32: 'public/logo-32.png',
    48: 'public/logo.png',
    128: 'public/logo-128.png',
  },
  action: {
    default_icon: {
      16: 'public/logo-16.png',
      32: 'public/logo-32.png',
      48: 'public/logo.png',
      128: 'public/logo-128.png',
    },
    default_popup: 'src/popup/index.html',
  },
  permissions: [
    'storage'
  ],
  web_accessible_resources: [{
    resources: ['src/page/mainWorld.ts'],
    matches: ['https://www.netflix.com/*'],
    use_dynamic_url: true
  }],
  content_scripts: [{
    js: ['src/content/inject.ts'],
    matches: ['https://www.netflix.com/*'],
    run_at: 'document_start'
  }],
})
