// This file is now replaced by inject.ts which uses the web-accessible script injection pattern
// The new pattern is more resilient and avoids CRXJS + Vite "world": "MAIN" issues

// For reference, the new architecture is:
// 1. inject.ts (ISOLATED world) - handles settings and injects MAIN world script
// 2. mainWorld.ts (MAIN world) - has direct access to window.netflix
// 3. Communication via custom events between worlds

console.log('[Netflix Skipper] Legacy main.tsx - now using inject.ts pattern');
