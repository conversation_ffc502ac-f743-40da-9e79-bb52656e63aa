import { NetflixVideoController } from './netflixController';

console.log('[Netflix Skipper] Content script loaded');

// Global sentry to prevent duplicate controller instances on SPA navigation
if (!(window as any).__netflixSkipperInitialized) {
  (window as any).__netflixSkipperInitialized = true;

  // Initialize the Netflix video controller when the page loads
  const controller = new NetflixVideoController();
  controller.initialize();
}
