// This script runs in the ISOLATED world and injects the MAIN world script
// It also handles settings communication between worlds

import { StorageManager, NetflixSkipperSettings, DEFAULT_SETTINGS } from '../utils/storage';

class NetflixSkipperInjector {
  private settings: NetflixSkipperSettings = DEFAULT_SETTINGS;
  private isInitialized = false;

  constructor() {
    this.handleSettingsRequest = this.handleSettingsRequest.bind(this);
  }

  /**
   * Initialize the injector
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('[Netflix Skipper] Initializing injector in ISOLATED world');

    // Load initial settings
    await this.loadSettings();

    // Listen for settings changes
    StorageManager.onSettingsChanged((newSettings) => {
      this.settings = newSettings;
      this.broadcastSettings();
      console.log('[Netflix Skipper] Settings updated in ISOLATED world:', newSettings);
    });

    // Listen for settings requests from MAIN world
    window.addEventListener('netflix-skipper-request-settings', this.handleSettingsRequest);

    // Inject the MAIN world script
    this.injectMainWorldScript();

    this.isInitialized = true;
    console.log('[Netflix Skipper] Injector initialized');
  }

  /**
   * Load settings from storage
   */
  private async loadSettings(): Promise<void> {
    try {
      this.settings = await StorageManager.getSettings();
    } catch (error) {
      console.error('[Netflix Skipper] Failed to load settings:', error);
      this.settings = DEFAULT_SETTINGS;
    }
  }

  /**
   * Handle settings request from MAIN world
   */
  private handleSettingsRequest(): void {
    this.broadcastSettings();
  }

  /**
   * Broadcast settings to MAIN world
   */
  private broadcastSettings(): void {
    window.dispatchEvent(new CustomEvent('netflix-skipper-settings-update', {
      detail: { settings: this.settings }
    }));
  }

  /**
   * Inject the MAIN world script
   */
  private injectMainWorldScript(): void {
    try {
      // The script path will be resolved by CRXJS during build
      const scriptPath = 'src/page/mainWorld.ts';

      const script = document.createElement('script');
      script.src = chrome.runtime.getURL(scriptPath);
      script.type = 'module';
      script.onload = () => {
        script.remove();
        console.log('[Netflix Skipper] MAIN world script injected and loaded');
        // Send initial settings after script loads
        setTimeout(() => this.broadcastSettings(), 100);
      };
      script.onerror = (error) => {
        console.error('[Netflix Skipper] Failed to load MAIN world script:', error);
      };

      (document.head || document.documentElement).appendChild(script);
    } catch (error) {
      console.error('[Netflix Skipper] Failed to inject MAIN world script:', error);
    }
  }

  /**
   * Cleanup when the injector is destroyed
   */
  destroy(): void {
    if (this.isInitialized) {
      window.removeEventListener('netflix-skipper-request-settings', this.handleSettingsRequest);
      
      // Call cleanup function in MAIN world if it exists
      if ((window as any).__netflixSkipperCleanup) {
        (window as any).__netflixSkipperCleanup();
      }
      
      this.isInitialized = false;
      console.log('[Netflix Skipper] Injector destroyed');
    }
  }
}

// Global sentry to prevent duplicate injector instances on SPA navigation
if (!(window as any).__netflixSkipperInjectorInitialized) {
  (window as any).__netflixSkipperInjectorInitialized = true;
  
  console.log('[Netflix Skipper] Injector script loaded');
  const injector = new NetflixSkipperInjector();
  injector.initialize();
}
