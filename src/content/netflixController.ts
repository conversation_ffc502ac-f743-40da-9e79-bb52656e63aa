import { StorageManager, NetflixSkipperSettings, DEFAULT_SETTINGS } from '../utils/storage';

// Declare Netflix global for TypeScript
declare global {
  interface Window {
    netflix?: {
      appContext: {
        state: {
          playerApp: {
            getAPI(): {
              videoPlayer: {
                getVideoPlayerBySessionId(sessionId: string): {
                  getCurrentTime(): number;
                  getDuration(): number;
                  seek(time: number): void;
                  play(): void;
                  pause(): void;
                  setVolume(volume: number): void;
                };
                getAllPlayerSessionIds(): string[];
              };
            };
          };
        };
      };
    };
  }
}

export class NetflixVideoController {
  private settings: NetflixSkipperSettings = DEFAULT_SETTINGS;
  private isInitialized = false;

  constructor() {
    this.handleKeyDown = this.handleKeyDown.bind(this);
    this.loadSettings();
  }

  /**
   * Initialize the controller
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('[Netflix Skipper] Initializing video controller');

    // Load settings from storage
    await this.loadSettings();

    // Listen for settings changes
    StorageManager.onSettingsChanged((newSettings) => {
      this.settings = newSettings;
      console.log('[Netflix Skipper] Settings updated:', newSettings);
    });

    // Add keyboard event listener
    document.addEventListener('keydown', this.handleKeyDown, true);

    this.isInitialized = true;
    console.log('[Netflix Skipper] Controller initialized with skip interval:', this.settings.skipInterval);
  }

  /**
   * Load settings from storage
   */
  private async loadSettings(): Promise<void> {
    try {
      this.settings = await StorageManager.getSettings();
    } catch (error) {
      console.error('[Netflix Skipper] Failed to load settings:', error);
      this.settings = DEFAULT_SETTINGS;
    }
  }

  /**
   * Handle keydown events
   */
  private handleKeyDown(event: KeyboardEvent): void {
    // Only handle arrow keys
    if (event.key !== 'ArrowLeft' && event.key !== 'ArrowRight') {
      return;
    }

    // Prevent default behavior
    event.preventDefault();
    // event.stopPropagation();
    // event.stopImmediatePropagation();

    // Handle the skip
    console.log('[Netflix Skipper] Skipping with interval:', this.settings.skipInterval);
    if (event.key === 'ArrowLeft') {
      this.skip(-this.settings.skipInterval);
    } else if (event.key === 'ArrowRight') {
      this.skip(this.settings.skipInterval);
    }
  }

  /**
   * Get Netflix player using the correct session ID approach
   */
  private getNetflixPlayer(): any {
    try {
      if (!window.netflix || !window.netflix.appContext) {
        console.log('[Netflix Skipper] Netflix API not available yet');
        return null;
      }

      const videoPlayerAPI = window.netflix.appContext.state.playerApp.getAPI().videoPlayer;
      const activeSessionId = videoPlayerAPI.getAllPlayerSessionIds()[0]

      if (!activeSessionId) {
        console.log('[Netflix Skipper] No active watch session found');
        return null;
      }

      const player = videoPlayerAPI.getVideoPlayerBySessionId(activeSessionId);
      console.log('[Netflix Skipper] Found active player for session:', activeSessionId);
      return player;

    } catch (error) {
      console.error('[Netflix Skipper] Error accessing Netflix API:', error);
      return null;
    }
  }

  /**
   * Skip function with proper error handling
   */
  private skip(deltaSec: number): void {
    const player = this.getNetflixPlayer();
    if (!player) {
      console.log('[Netflix Skipper] No player available for skip');
      return;
    }

    try {
      const currentTime = player.getCurrentTime();
      const duration = player.getDuration();
      let newTime;

      if (deltaSec < 0) {
        // Skip backward
        newTime = Math.max(currentTime + (deltaSec * 1000), 0);
      } else {
        // Skip forward
        newTime = Math.min(currentTime + (deltaSec * 1000), duration);
      }

      console.log('[Netflix Skipper] Seeking from', Math.round(currentTime/1000), 's to', Math.round(newTime/1000), 's');
      player.seek(newTime);

      // Show indicator
      this.showSkipIndicator(deltaSec < 0 ? 'backward' : 'forward', Math.abs(deltaSec));

    } catch (error) {
      console.error('[Netflix Skipper] Error during seek:', error);
    }
  }



  /**
   * Show a visual indicator for the skip action
   */
  private showSkipIndicator(direction: 'forward' | 'backward', seconds: number): void {
    // Remove any existing indicator
    const existingIndicator = document.getElementById('netflix-skipper-indicator');
    if (existingIndicator) {
      existingIndicator.remove();
    }

    // Create new indicator
    const indicator = document.createElement('div');
    indicator.id = 'netflix-skipper-indicator';
    indicator.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      font-family: Arial, sans-serif;
      font-size: 16px;
      font-weight: bold;
      z-index: 10000;
      pointer-events: none;
      transition: opacity 0.3s ease;
    `;
    
    const arrow = direction === 'forward' ? '→' : '←';
    indicator.textContent = `${arrow} ${seconds}s`;
    
    document.body.appendChild(indicator);
    
    // Remove after 1 second
    setTimeout(() => {
      indicator.style.opacity = '0';
      setTimeout(() => {
        if (indicator.parentNode) {
          indicator.parentNode.removeChild(indicator);
        }
      }, 300);
    }, 700);
  }

  /**
   * Cleanup when the controller is destroyed
   */
  destroy(): void {
    if (this.isInitialized) {
      document.removeEventListener('keydown', this.handleKeyDown, true);
      this.isInitialized = false;
      console.log('[Netflix Skipper] Controller destroyed');
    }
  }
}
