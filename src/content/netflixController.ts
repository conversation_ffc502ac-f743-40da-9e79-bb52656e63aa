import { StorageManager, NetflixSkipperSettings, DEFAULT_SETTINGS } from '../utils/storage';

// Declare Netflix global for TypeScript
declare global {
  interface Window {
    netflix?: {
      appContext: {
        state: {
          playerApp: {
            getAPI(): {
              videoPlayer: {
                getVideoPlayerBySessionId(sessionId: string): {
                  getCurrentTime(): number;
                  getDuration(): number;
                  seek(time: number): void;
                  play(): void;
                  pause(): void;
                  setVolume(volume: number): void;
                };
                getAllPlayerSessionIds(): string[];
              };
            };
          };
        };
      };
    };
  }
}

export class NetflixVideoController {
  private settings: NetflixSkipperSettings = DEFAULT_SETTINGS;
  private isInitialized = false;
  private skipIndicator: HTMLElement | null = null;

  constructor() {
    this.handleKeyDown = this.handleKeyDown.bind(this);
  }

  /**
   * Initialize the controller
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('[Netflix Skipper] Initializing video controller');

    // Load settings from storage
    await this.loadSettings();

    // Listen for settings changes
    StorageManager.onSettingsChanged((newSettings) => {
      this.settings = newSettings;
      console.log('[Netflix Skipper] Settings updated:', newSettings);
    });

    // Add keyboard event listener
    document.addEventListener('keydown', this.handleKeyDown, true);

    this.isInitialized = true;
    console.log('[Netflix Skipper] Controller initialized with skip interval:', this.settings.skipInterval);
  }

  /**
   * Load settings from storage
   */
  private async loadSettings(): Promise<void> {
    try {
      this.settings = await StorageManager.getSettings();
    } catch (error) {
      console.error('[Netflix Skipper] Failed to load settings:', error);
      this.settings = DEFAULT_SETTINGS;
    }
  }

  /**
   * Handle keydown events
   */
  private handleKeyDown(event: KeyboardEvent): void {
    // Only handle arrow keys
    if (event.key !== 'ArrowLeft' && event.key !== 'ArrowRight') {
      return;
    }

    // Ignore repeat events (hold-to-seek quirk)
    if (event.repeat) {
      return;
    }

    // Skip if focus is in form fields
    const target = event.target as HTMLElement;
    if (target && (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.contentEditable === 'true'
    )) {
      return;
    }

    // Prevent default behavior and stop propagation
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();

    // Handle the skip
    console.log('[Netflix Skipper] Skipping with interval:', this.settings.skipInterval);
    if (event.key === 'ArrowLeft') {
      this.skip(-this.settings.skipInterval);
    } else if (event.key === 'ArrowRight') {
      this.skip(this.settings.skipInterval);
    }
  }

  /**
   * Get Netflix player using the correct session ID approach
   */
  private getNetflixPlayer(): any {
    try {
      if (!window.netflix || !window.netflix.appContext) {
        console.log('[Netflix Skipper] Netflix API not available yet');
        return null;
      }

      const videoPlayerAPI = window.netflix.appContext.state.playerApp.getAPI().videoPlayer;
      const sessionIds = videoPlayerAPI.getAllPlayerSessionIds();
      const activeSessionId = sessionIds.find(id => id.startsWith('watch-')) ?? sessionIds[0];

      if (!activeSessionId) {
        console.log('[Netflix Skipper] No active watch session found');
        return null;
      }

      const player = videoPlayerAPI.getVideoPlayerBySessionId(activeSessionId);
      console.log('[Netflix Skipper] Found active player for session:', activeSessionId);
      return player;

    } catch (error) {
      console.error('[Netflix Skipper] Error accessing Netflix API:', error);
      return null;
    }
  }

  /**
   * Skip function with proper error handling
   */
  private skip(deltaSec: number): void {
    const player = this.getNetflixPlayer();
    if (!player) {
      console.log('[Netflix Skipper] No player available for skip');
      return;
    }

    try {
      const currentTime = player.getCurrentTime();
      const duration = player.getDuration();
      let newTime;

      if (deltaSec < 0) {
        // Skip backward
        newTime = Math.max(currentTime + (deltaSec * 1000), 0);
      } else {
        // Skip forward
        newTime = Math.min(currentTime + (deltaSec * 1000), duration);
      }

      console.log('[Netflix Skipper] Seeking from', Math.round(currentTime/1000), 's to', Math.round(newTime/1000), 's');
      player.seek(newTime);

      // Show indicator
      this.showSkipIndicator(deltaSec < 0 ? 'backward' : 'forward', Math.abs(deltaSec));

    } catch (error) {
      console.error('[Netflix Skipper] Error during seek:', error);
    }
  }



  /**
   * Show a visual indicator for the skip action
   */
  private showSkipIndicator(direction: 'forward' | 'backward', seconds: number): void {
    // Create indicator if it doesn't exist
    if (!this.skipIndicator) {
      this.skipIndicator = document.createElement('div');
      this.skipIndicator.id = 'netflix-skipper-indicator';
      this.skipIndicator.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-family: Arial, sans-serif;
        font-size: 16px;
        font-weight: bold;
        z-index: 10000;
        pointer-events: none;
        transition: opacity 0.3s ease;
        opacity: 0;
      `;
      document.body.appendChild(this.skipIndicator);
    }

    // Update content and show
    const arrow = direction === 'forward' ? '→' : '←';
    this.skipIndicator.textContent = `${arrow} ${seconds}s`;
    this.skipIndicator.style.opacity = '1';

    // Hide after 1 second
    setTimeout(() => {
      if (this.skipIndicator) {
        this.skipIndicator.style.opacity = '0';
      }
    }, 700);
  }

  /**
   * Cleanup when the controller is destroyed
   */
  destroy(): void {
    if (this.isInitialized) {
      document.removeEventListener('keydown', this.handleKeyDown, true);

      // Clean up skip indicator
      if (this.skipIndicator && this.skipIndicator.parentNode) {
        this.skipIndicator.parentNode.removeChild(this.skipIndicator);
        this.skipIndicator = null;
      }

      this.isInitialized = false;
      console.log('[Netflix Skipper] Controller destroyed');
    }
  }
}
