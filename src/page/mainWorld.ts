// This script runs in the MAIN world and has direct access to window.netflix
// It communicates with the isolated world content script via custom events

interface NetflixSkipperSettings {
  skipInterval: number;
}

const DEFAULT_SETTINGS: NetflixSkipperSettings = {
  skipInterval: 5
};

// Declare Netflix global for TypeScript
declare global {
  interface Window {
    netflix?: {
      appContext: {
        state: {
          playerApp: {
            getAPI(): {
              videoPlayer: {
                getVideoPlayerBySessionId(sessionId: string): {
                  getCurrentTime(): number;
                  getDuration(): number;
                  seek(time: number): void;
                  play(): void;
                  pause(): void;
                  setVolume(volume: number): void;
                };
                getAllPlayerSessionIds(): string[];
              };
            };
          };
        };
      };
    };
  }
}

class NetflixVideoController {
  private settings: NetflixSkipperSettings = DEFAULT_SETTINGS;
  private isInitialized = false;
  private skipIndicator: HTMLElement | null = null;

  constructor() {
    this.handleKeyDown = this.handleKeyDown.bind(this);
    this.handleSettingsUpdate = this.handleSettingsUpdate.bind(this);
  }

  /**
   * Initialize the controller
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('[Netflix Skipper] Initializing video controller in MAIN world');

    // Request settings from isolated world
    this.requestSettings();

    // Listen for settings updates from isolated world
    window.addEventListener('netflix-skipper-settings-update', this.handleSettingsUpdate);

    // Add keyboard event listener
    document.addEventListener('keydown', this.handleKeyDown, true);

    this.isInitialized = true;
    console.log('[Netflix Skipper] Controller initialized in MAIN world');
  }

  /**
   * Request settings from isolated world content script
   */
  private requestSettings(): void {
    window.dispatchEvent(new CustomEvent('netflix-skipper-request-settings'));
  }

  /**
   * Handle settings update from isolated world
   */
  private handleSettingsUpdate(event: Event): void {
    const customEvent = event as CustomEvent;
    if (customEvent.detail && customEvent.detail.settings) {
      this.settings = customEvent.detail.settings;
      console.log('[Netflix Skipper] Settings updated in MAIN world:', this.settings);
    }
  }

  /**
   * Handle keydown events
   */
  private handleKeyDown(event: KeyboardEvent): void {
    // Only handle arrow keys
    if (event.key !== 'ArrowLeft' && event.key !== 'ArrowRight') {
      return;
    }

    // Ignore repeat events (hold-to-seek quirk)
    if (event.repeat) {
      return;
    }

    // Skip if focus is in form fields
    const target = event.target as HTMLElement;
    if (target && (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.contentEditable === 'true'
    )) {
      return;
    }

    // Prevent default behavior and stop propagation
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();

    // Handle the skip
    console.log('[Netflix Skipper] Skipping with interval:', this.settings.skipInterval);
    if (event.key === 'ArrowLeft') {
      this.skip(-this.settings.skipInterval);
    } else if (event.key === 'ArrowRight') {
      this.skip(this.settings.skipInterval);
    }
  }

  /**
   * Get Netflix player using the correct session ID approach
   */
  private getNetflixPlayer(): any {
    try {
      if (!window.netflix || !window.netflix.appContext) {
        console.log('[Netflix Skipper] Netflix API not available yet');
        return null;
      }

      const videoPlayerAPI = window.netflix.appContext.state.playerApp.getAPI().videoPlayer;
      const sessionIds = videoPlayerAPI.getAllPlayerSessionIds();
      const activeSessionId = sessionIds.find(id => id.startsWith('watch-')) ?? sessionIds[0];

      if (!activeSessionId) {
        console.log('[Netflix Skipper] No active watch session found');
        return null;
      }

      const player = videoPlayerAPI.getVideoPlayerBySessionId(activeSessionId);
      console.log('[Netflix Skipper] Found active player for session:', activeSessionId);
      return player;

    } catch (error) {
      console.error('[Netflix Skipper] Error accessing Netflix API:', error);
      return null;
    }
  }

  /**
   * Skip function with proper error handling
   */
  private skip(deltaSec: number): void {
    const player = this.getNetflixPlayer();
    if (!player) {
      console.log('[Netflix Skipper] No player available for skip');
      return;
    }

    try {
      const currentTime = player.getCurrentTime();
      const duration = player.getDuration();
      let newTime;

      if (deltaSec < 0) {
        // Skip backward
        newTime = Math.max(currentTime + (deltaSec * 1000), 0);
      } else {
        // Skip forward
        newTime = Math.min(currentTime + (deltaSec * 1000), duration);
      }

      console.log('[Netflix Skipper] Seeking from', Math.round(currentTime/1000), 's to', Math.round(newTime/1000), 's');
      player.seek(newTime);

      // Show indicator
      this.showSkipIndicator(deltaSec < 0 ? 'backward' : 'forward', Math.abs(deltaSec));

    } catch (error) {
      console.error('[Netflix Skipper] Error during seek:', error);
    }
  }

  /**
   * Show a visual indicator for the skip action
   */
  private showSkipIndicator(direction: 'forward' | 'backward', seconds: number): void {
    // Create indicator if it doesn't exist
    if (!this.skipIndicator) {
      this.skipIndicator = document.createElement('div');
      this.skipIndicator.id = 'netflix-skipper-indicator';
      this.skipIndicator.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-family: Arial, sans-serif;
        font-size: 16px;
        font-weight: bold;
        z-index: 10000;
        pointer-events: none;
        transition: opacity 0.3s ease;
        opacity: 0;
      `;
      document.body.appendChild(this.skipIndicator);
    }

    // Update content and show
    const arrow = direction === 'forward' ? '→' : '←';
    this.skipIndicator.textContent = `${arrow} ${seconds}s`;
    this.skipIndicator.style.opacity = '1';
    
    // Hide after 1 second
    setTimeout(() => {
      if (this.skipIndicator) {
        this.skipIndicator.style.opacity = '0';
      }
    }, 700);
  }

  /**
   * Cleanup when the controller is destroyed
   */
  destroy(): void {
    if (this.isInitialized) {
      document.removeEventListener('keydown', this.handleKeyDown, true);
      window.removeEventListener('netflix-skipper-settings-update', this.handleSettingsUpdate);
      
      // Clean up skip indicator
      if (this.skipIndicator && this.skipIndicator.parentNode) {
        this.skipIndicator.parentNode.removeChild(this.skipIndicator);
        this.skipIndicator = null;
      }
      
      this.isInitialized = false;
      console.log('[Netflix Skipper] Controller destroyed in MAIN world');
    }
  }
}

// Initialize the controller when this script loads
console.log('[Netflix Skipper] MAIN world script loaded');
const controller = new NetflixVideoController();
controller.initialize();

// Global cleanup function
(window as any).__netflixSkipperCleanup = () => {
  controller.destroy();
};
